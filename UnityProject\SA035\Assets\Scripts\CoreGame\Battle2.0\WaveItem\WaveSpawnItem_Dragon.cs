using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using BehaviorDesigner.Runtime.Tasks.Unity.UnityGameObject;
using DG.Tweening;
using Game.Timeline;
using Qarth;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Playables;


namespace GameWish.Game
{
	public class SpawnBodyGroup
	{
		public int groupId;
		public int hp;
		public TDDungeonDragon data;
		public List<int> bodys = new List<int>();
	}
	public class WaveSpawnItem_Dragon : WaveSpawnItem
	{
		private List<TDDungeonDragon> m_DragonDatas = new List<TDDungeonDragon>();
		private List<SpawnBodyGroup> m_LstDragonSpawn = new List<SpawnBodyGroup>();
		private List<DragonBodyGroup> m_LstDragonGroups = new List<DragonBodyGroup>();
		private List<int> m_GroupIDs = new List<int>();
		private List<DragonBody> m_LstDragonBodys = new List<DragonBody>();

		private GameObject m_DragonHead;
		private GameObject m_DragonHeadSub;
		public WaveSpawnItem_Dragon(int index, TDWave waveData, GameWaveController holder) : base(index, waveData, holder)
		{
			AddressableResMgr.S.InstantiateAsync("DragonHead", (res, state) =>
			{
				if (state)
				{
					m_DragonHead = res;
					m_DragonHead.SetActive(false);
					m_DragonHead.transform.position = Vector3.one * 10000;
					m_DragonHead.transform.localScale = Vector3.one * 0.72f;
					m_DragonHeadSub = m_DragonHead.transform.GetChild(0).gameObject;
				}
			});
			m_DragonDatas = (GameLogicMgr.S.gameLevel.levelCtrl as GameLevelController_Dragon).dragonTableDatas;
			m_WaveAllCount = m_DragonDatas.Count * 5;
			m_LstDragonSpawn.Clear();
			for (int i = 0; i < m_DragonDatas.Count; i++)
			{
				SpawnBodyGroup gp = new SpawnBodyGroup();
				gp.groupId = i;
				gp.data = m_DragonDatas[i];
				gp.hp = m_DragonDatas[i].hp;
				for (int x = 0; x < 5; x++)
				{
					gp.bodys.Add(m_DragonDatas[i].bodyId);
				}

				DragonBodyGroup bgp = new DragonBodyGroup();
				bgp.groupId = i;
				bgp.tableId = m_DragonDatas[i].id;
				bgp.commonBlood = m_DragonDatas[i].hp;

				m_LstDragonSpawn.Add(gp);
				m_LstDragonGroups.Add(bgp);
				m_GroupIDs.Add(i);
			}
			m_SpawnDragonGroupIndex = 0;
			m_LstDragonBodys.Clear();
		}

		private float m_SpawnDragonInterval = GameDefineHelper.DRAGON_SPAWN_INTERVAL;
		private float m_SpawnDragonTime = 0f;
		private int m_SpawnDragonGroupIndex = 0;
		private Vector3 m_HeadLeftScale = new Vector3(-1, 1, 1);
		private Vector3 m_HeadRightScale = new Vector3(1, 1, 1);

		private List<int> m_NeedRemoveGroup = new List<int>();
		float m_LastHeadX = -2.5f;
		float m_CheckTime = 0f;//1秒check一次

		public override void Tick(float deltaTime)
		{
			if (!m_StartSpawn) return;
			m_SpawnDelay -= deltaTime;
			m_TimeCountDown += deltaTime;
			if (m_TimeCountDown > 1f && !m_HasShowTip)
			{
				m_TimeCountDown = 0;
				EventSystem.S.Send(EventID.OnWaveTimeUpdate, (int)m_SpawnDelay);
			}
			if (m_SpawnDelay > 0) return;
			if (!m_HasShowTip)
			{
				EventSystem.S.Send(EventID.OnWaveTimeUpdate, (int)m_SpawnDelay, true);
				if (!bossWave && m_Wave == 0)
				{
					EventSystem.S.Send(EventID.OnWaveShow, m_Wave);
				}
				m_HasShowTip = true;
			}
			if (m_Holder.holder.waveFromTimeline && !m_StartPlayTimeline && !m_SpawnFinish)
			{
				m_Holder.holder.levelMono.playableDirector.Play();
				var playable = m_Holder.holder.levelMono.playableDirector.playableGraph.GetRootPlayable(0);
				m_StartPlayTimeline = true;
				EventSystem.S.Send(EventID.OnTimeScaleChange);
				waveSingleCoin = 0;
				EventSystem.S.Send(EventID.OnWaveStart, m_Wave);
			}
			m_SpawnDragonTime += deltaTime;
			m_CheckTime += deltaTime;
			if (m_SpawnDragonTime >= m_SpawnDragonInterval)
			{
				//按数据生成龙
				m_SpawnDragonTime = 0f;

				if (m_SpawnDragonGroupIndex >= m_LstDragonSpawn.Count) return;
				var dragon = SpawnDragonBody(m_LstDragonSpawn[m_SpawnDragonGroupIndex].bodys[0]);
				m_LstDragonGroups[m_SpawnDragonGroupIndex].lstBody.Add(dragon);
				dragon.group = m_LstDragonGroups[m_SpawnDragonGroupIndex];
				dragon.indexInGroup = m_LstDragonSpawn[m_SpawnDragonGroupIndex].bodys.Count - 1;
				m_LstDragonSpawn[m_SpawnDragonGroupIndex].bodys.RemoveAt(0);
				m_LstDragonBodys.Add(dragon);
				if (m_LstDragonSpawn[m_SpawnDragonGroupIndex].bodys.Count == 0)
				{
					m_SpawnDragonGroupIndex++;
				}
			}
			//身体位置判断
			if (m_NeedChangePos && m_NeedChangeGroupId >= 0)
			{
				//m_CheckTime = 0f;
				//先清理数组

				for (int i = 0; i < m_LstDragonBodys.Count; i++)
				{
					if (m_NeedRemoveGroup.Contains(m_LstDragonBodys[i].group.groupId))
					{
						m_LstDragonBodys.Remove(m_LstDragonBodys[i]);
						i--;
					}
				}

				DragonBody lastRole = null;
				for (int i = m_LstDragonBodys.Count - 1; i >= 0; i--)
				{
					if (m_LstDragonBodys[i].group.groupId > m_NeedChangeGroupId)
					{
						lastRole = m_LstDragonBodys[i];
						continue;
					}
					if (lastRole != null)
					{
						m_LstDragonBodys[i].bodyMove.ForceChangePos(lastRole);
					}
					lastRole = m_LstDragonBodys[i];
				}
				m_NeedChangePos = false;
			}
			else
			{
				// if (m_CheckTime > 2f)
				// {
				// 	m_CheckTime = 0f;
				// 	DragonBody lastRole = null;
				// 	for (int i = m_LstDragonBodys.Count - 1; i >= 0; i--)
				// 	{
				// 		if (lastRole != null)
				// 		{
				// 			m_LstDragonBodys[i].bodyMove.ForceChangePos(lastRole);
				// 		}
				// 		lastRole = m_LstDragonBodys[i];
				// 	}
				// }
			}

			//龙头位置判断
			if (m_DragonHead != null && m_LstDragonBodys.Count > 0)
			{
				if (m_LstDragonBodys[0].roleRenderComponent != null)
				{
					if (m_CheckTime > 4f)
					{
						m_DragonHead.SetActive(true);
						m_DragonHeadSub.SetObjActive(true);
					}
					m_DragonHead.transform.position = m_LstDragonBodys[0].lastPos;
					if (m_LastHeadX > m_DragonHead.transform.position.x)
					{
						m_DragonHeadSub.transform.localScale = m_HeadLeftScale;
					}
					else if (m_LastHeadX < m_DragonHead.transform.position.x)
					{
						m_DragonHeadSub.transform.localScale = m_HeadRightScale;
					}
					m_LastHeadX = m_DragonHead.transform.position.x;

				}
			}
		}

		public override int WaveSingleExp(int hp)
		{
			return 0;
		}

		public override void ForceStartSpawn()
		{

		}

		public DragonBody SpawnDragonBody(int bodyId)
		{
			int roleId = bodyId;
			//Log.e("roleId {0}", objectID);
			TDEnemy eData = TDEnemyTable.GetData(roleId);
			RoleProperties info = new RoleProperties(true, 1, 0, 0);
			//赋值info
			info.atk = (int)(eData.commonData.atk * (1 + m_WaveData.enemyAtkUpRate.RealValue(GameValueType.Float)));
			info.hp = (int)(10000 * (1 + m_WaveData.enemyHpUpRate.RealValue(GameValueType.Float)));

			info.moveSpeed = eData.commonData.moveSpeed;
			info.atkRange = eData.commonData.atkRange;
			info.atkInterval = eData.commonData.atkInterval;
			info.dotAtk = (int)(eData.commonData.dotAtk * (1 + m_WaveData.enemyAtkUpRate.RealValue(GameValueType.Float)));
			info.dotInterval = eData.commonData.dotInterval;
			info.dotTime = eData.commonData.dotTime;
			info.searchingRange = eData.commonData.searchingRange;

			var enemy = m_Holder.SpawnEnemy(roleId, info) as DragonBody;
			m_WaveEnemies.Add(enemy.instanceId);
			waveSpawnEnemieRecord.Add(enemy.instanceId);
			m_WaveSpawnCount++;

			if (m_WaveSpawnCount >= m_WaveAllCount)
			{
				m_SpawnFinish = true;
				m_StartSpawn = false;
				if (m_Holder != null && m_Holder.holder != null && m_Holder.holder.levelMono != null)
				{
					m_Holder.holder.levelMono.playableDirector?.Stop();
				}
				EventSystem.S.Send(EventID.OnWaveSpawnFinished, m_Wave);
			}
			return enemy;
		}

		private bool m_NeedChangePos = false;
		private int m_NeedChangeGroupId = -1;
		protected override void OnRoleDie(int key, params object[] param)
		{
			int roleInsId = (int)param[0];
			int roleId = (int)param[1];

			for (int i = 0; i < m_LstDragonBodys.Count; i++)
			{
				if (m_LstDragonBodys[i].instanceId == roleInsId)
				{
					m_NeedChangeGroupId = m_LstDragonBodys[i].group.groupId;
					if (!m_NeedRemoveGroup.Contains(m_NeedChangeGroupId))
					{
						m_NeedRemoveGroup.Add(m_NeedChangeGroupId);
						//发送技能卡事件
						EventSystem.S.Send(EventID.OnShowSpell1of3);
						EventSystem.S.Send(EventID.OnDragonModeLengthChange, m_NeedRemoveGroup.Count * 10);
						EventSystem.S.Send(EventID.OnDragonModeGroupChange, m_LstDragonBodys[i].group.tableId);
					}
					m_NeedChangePos = true;
					break;
				}
			}


			if (m_WaveEnemies.Contains(roleInsId))
			{
				m_WaveEnemies.Remove(roleInsId);
			}
			if (m_WaveEnemies.Count == 0 && m_SpawnFinish)
			{
				// Log.e("清空波次怪物");
				EventSystem.S.UnRegister(EventID.OnEnemyDespawn, OnRoleDie);
				EventSystem.S.Send(EventID.OnWaveClear, m_Wave);
			}
			m_StartPlayTimeline = false;
		}

		public override void OnDestroyed()
		{
			base.OnDestroyed();
			GameObject.Destroy(m_DragonHead);
			m_DragonHead = null;
		}
	}

}