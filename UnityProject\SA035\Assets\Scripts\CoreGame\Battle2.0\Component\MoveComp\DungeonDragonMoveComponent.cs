using System.Collections;
using System.Collections.Generic;
using Qarth;
using UnityEngine;


namespace GameWish.Game
{
	public class DungeonDragonMoveComponent : RoleMoveComponent
	{
		private enum MoveDirection
		{
			Left = 0,
			Right,
			Down,
		}
		private float m_XLeft = -3.5f;
		private float m_XRight = 3.5f;
		private float m_YInterval = 2f;
		private List<MoveDirection> m_PathDirection = new List<MoveDirection>()
		{
			MoveDirection.Right,//10
			MoveDirection.Down,
			MoveDirection.Left,//8
			MoveDirection.Down,
			MoveDirection.Right,//6
			MoveDirection.Down,
			MoveDirection.Left,//4
			MoveDirection.Down,
			MoveDirection.Right,//2
			MoveDirection.Down,
			MoveDirection.Left,//0
			MoveDirection.Down,
			MoveDirection.Right,//-2
			MoveDirection.Down,
			MoveDirection.Left,//-4
			MoveDirection.Down
		};

		//right->down->left->down->right->

		private MoveDirection m_Direction = MoveDirection.Right;
		protected override void OnLoadFinish(int key, params object[] param)
		{
			if (m_Role.instanceId == (int)param[0])
			{
				var pos = m_Role.forcePos;
				m_Role.roleRenderComponent.transform.position = new Vector3(-2.5f, 10f, 0);
				m_Role.roleRenderComponent.transform.SetZ(-0.2f);
				canMove = true;
			}
		}
		private int m_CurPathIndex = 0;
		public int curPathIndex => m_CurPathIndex;
		float moveDownDis = 0f;
		private float m_DeltaTime;
		public override void Tick(float deltaTime)
		{
			if (deltaTime > 0)
			{
				m_DeltaTime = deltaTime;
			}
			if (!canMove) return;
			if (m_Role.roleRenderComponent.transform == null) return;
			//S型路线移动，先往左下角移动，x到达边界，就改为反方向右下角，以此类推
			if (m_CurPathIndex >= m_PathDirection.Count - 1)
			{
				//到终点了 游戏结束
				EventSystem.S.Send(EventID.OnBattleEnd, false);
				return;
			}
			m_Direction = m_PathDirection[m_CurPathIndex];
			switch (m_Direction)
			{
				case MoveDirection.Left:
					m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.left * deltaTime * m_Role.runtimeData.moveSpeed);
					m_Role.roleRenderComponent.ChangeTurn(true, true);
					break;
				case MoveDirection.Right:
					m_Role.roleRenderComponent.ChangeTurn(true, false);
					m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.right * deltaTime * m_Role.runtimeData.moveSpeed);
					break;
				case MoveDirection.Down:
					moveDownDis += (deltaTime * m_Role.runtimeData.moveSpeed);
					m_Role.roleRenderComponent.transform.position += (Vector3)(Vector2.down * deltaTime * m_Role.runtimeData.moveSpeed);
					break;
			}

			//往下走
			if (m_Direction == MoveDirection.Right && m_Role.roleRenderComponent.transform.position.x >= m_XRight)
			{
				m_CurPathIndex++;
				//Log.e(m_CurPathIndex);
				moveDownDis = 0f;
			}
			else if (m_Direction == MoveDirection.Left && m_Role.roleRenderComponent.transform.position.x <= m_XLeft)
			{
				m_CurPathIndex++;
				//Log.e(m_CurPathIndex);
				moveDownDis = 0f;
			}
			else if (m_Direction == MoveDirection.Down && moveDownDis >= m_YInterval)
			{
				m_CurPathIndex++;
				//Log.e(m_CurPathIndex);
			}
		}
		private float m_SpawnDragonInterval = GameDefineHelper.DRAGON_SPAWN_INTERVAL;
		public void ForceChangePos(DragonBody lastBody)
		{
			if (lastBody == null) return;
			if (lastBody.bodyMove == null || lastBody.bodyMove.curPathIndex > m_PathDirection.Count - 1)
			{
				return;
			}
			int lastMovePathIndex = lastBody.bodyMove.curPathIndex;
			var direction = m_PathDirection[lastMovePathIndex];
			var dir = Vector2.left;
			switch (direction)
			{
				case MoveDirection.Left:
					dir = Vector2.left;
					break;
				case MoveDirection.Right:
					dir = Vector2.right;
					break;
				case MoveDirection.Down:
					dir = Vector2.down;
					break;
			}
			if (lastBody.roleRenderComponent == null) return;
			if (lastBody.roleRenderComponent.transform == null) return;
			if (m_Role.roleRenderComponent == null) return;
			if (m_Role.roleRenderComponent.transform == null) return;
			float moveDis = (dir * m_SpawnDragonInterval * m_DeltaTime * lastBody.runtimeData.moveSpeed).magnitude;
			var targetPos = lastBody.roleRenderComponent.transform.position + (Vector3)(dir * m_SpawnDragonInterval * m_DeltaTime * lastBody.runtimeData.moveSpeed);
			float outDis = 0;
			float firstDis = moveDis;
			//往下走
			if (direction == MoveDirection.Right && targetPos.x >= m_XRight)
			{
				lastMovePathIndex++;
				//先移动到边界
				outDis = targetPos.x - m_XRight;
				firstDis = moveDis - outDis;
			}
			else if (direction == MoveDirection.Left && targetPos.x <= m_XLeft)
			{
				lastMovePathIndex++;
				outDis = m_XLeft - targetPos.x;
				firstDis = moveDis - outDis;
			}
			else if (direction == MoveDirection.Down && targetPos.y - lastBody.roleRenderComponent.transform.position.y >= m_YInterval)
			{
				lastMovePathIndex++;
				outDis = targetPos.y - lastBody.roleRenderComponent.transform.position.y - m_YInterval;
				firstDis = moveDis - outDis;
			}
			m_Role.roleRenderComponent.transform.position = lastBody.roleRenderComponent.transform.position + (Vector3)(dir * firstDis);

			direction = m_PathDirection[lastMovePathIndex];
			switch (direction)
			{
				case MoveDirection.Left:
					dir = Vector2.left;
					break;
				case MoveDirection.Right:
					dir = Vector2.right;
					break;
				case MoveDirection.Down:
					dir = Vector2.down;
					break;
			}
			//检查是否要更换index和方向
			m_Role.roleRenderComponent.transform.position += (Vector3)(dir * outDis);
			m_Direction = direction;
			m_CurPathIndex = lastMovePathIndex;
		}

		// public Vector3 GetShouldBePos(DragonBody lastBody, int interval)
		// {

		// }

		public override void HitBack(float backDis, System.Action onComplete = null)
		{
			//免疫击退效果
		}

		public override void Knockback(float backDis)
		{

		}

	}

}